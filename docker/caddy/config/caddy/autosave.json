{"apps": {"http": {"servers": {"srv0": {"listen": [":443"], "routes": [{"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:54322"}]}]}]}], "match": [{"host": ["supabase-postgres.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:54324"}]}]}]}], "match": [{"host": ["supabase-inbucket.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:54321"}]}]}]}], "match": [{"host": ["supabase-api.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:7700"}]}]}]}], "match": [{"host": ["meilisearch.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:54323"}]}]}]}], "match": [{"host": ["supabase.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:4040"}]}]}]}], "match": [{"host": ["backend.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "load_balancing": {"try_duration": 3000000000, "try_interval": 250000000}, "upstreams": [{"dial": "host.docker.internal:3040"}]}]}]}], "match": [{"host": ["web-app.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:3030"}]}]}]}], "match": [{"host": ["website.constellatio.localhost"]}], "terminal": true}, {"handle": [{"handler": "subroute", "routes": [{"handle": [{"handler": "reverse_proxy", "upstreams": [{"dial": "host.docker.internal:5540"}]}]}]}], "match": [{"host": ["redis.constellatio.localhost"]}], "terminal": true}], "tls_connection_policies": [{"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["supabase-postgres.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["supabase-inbucket.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["supabase-api.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["meilisearch.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["supabase.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["backend.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["web-app.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["website.constellatio.localhost"]}}, {"certificate_selection": {"any_tag": ["cert0"]}, "match": {"sni": ["redis.constellatio.localhost"]}}, {}]}}}, "tls": {"certificates": {"load_files": [{"certificate": "/data/certs/constellatio.localhost.cert.pem", "key": "/data/certs/constellatio.localhost.key.pem", "tags": ["cert0"]}]}}}}