import ContentWrapper from "~/components/helpers/contentWrapper/ContentWrapper.tsx";
import DashboardCallToActionBlock from "~/components/organisms/dashboardCallToActionBlock/DashboardCallToActionBlock.tsx";
import DashboardHeader from "~/components/organisms/dashboardHeader/DashboardHeader.tsx";
import DashboardLastEditedBlock from "~/components/organisms/dashboardLastEditedBlock/DashboardLastEditedBlock.tsx";
import DashboardPersonalSpaceBlock from "~/components/organisms/dashboardPersonalSpaceBlock/DashboardPersonalSpaceBlock.tsx";
import { UnsubscribedOrTrial } from "~/components/organisms/UnsubscribedOrTrial/UnsubscribedOrTrial.tsx";

import { type FunctionComponent } from "react";

import * as styles from "./DashboardPage.styles.ts";

const DashboardPage: FunctionComponent = () => (
  <div>
    <DashboardHeader />
    <ContentWrapper stylesOverrides={styles.sections}>
      <UnsubscribedOrTrial />
      {/* <DashboardCallToActionBlock />*/}
      <DashboardLastEditedBlock />
      <DashboardPersonalSpaceBlock />
    </ContentWrapper>
  </div>
);

export default DashboardPage;
