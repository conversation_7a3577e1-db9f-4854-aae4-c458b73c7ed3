/* eslint-disable max-lines */
import { createEnv } from "@t3-oss/env-core";
import { z } from "zod";

import {
  urlValidation,
  deploymentEnvironmentValidation,
  googleTagManagerValidation,
  isMaintenanceModeValidation,
  posthogConfigValidation,
} from "./_utils.js";

if (process.env.JSON_ENV)
{
  const jsonEnvParsed = JSON.parse(process.env.JSON_ENV);

  if (!jsonEnvParsed || typeof jsonEnvParsed !== "object")
  {
    throw new Error(`JSON environment variables were null or not an object: ${JSON.stringify(jsonEnvParsed)}`);
  }

  process.env = { ...process.env, ...jsonEnvParsed };
  delete process.env.JSON_ENV;
}

export const env = createEnv({
  client: {
    NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT: deploymentEnvironmentValidation,
    NEXT_PUBLIC_SHARED_TRIAL_PERIOD_IN_DAYS: z.string().pipe(z.coerce.number().int().min(1).max(730)),
    NEXT_PUBLIC_SHARED_WEBAPP_URL: urlValidation,
    NEXT_PUBLIC_SHARED_WEBSITE_URL: urlValidation,
    NEXT_PUBLIC_WEBAPP_APP_NAME: z.string(),
    NEXT_PUBLIC_WEBAPP_BACKEND_URL: urlValidation,
    NEXT_PUBLIC_WEBAPP_CLOUD_STORAGE_SIGNED_GET_URL_STALE_TIME_SECONDS: z.string().pipe(z.coerce.number().int().min(1)),
    NEXT_PUBLIC_WEBAPP_CONTENT_ITEMS_VIEWS_HISTORY_DAYS_LIMIT: z.string().pipe(z.coerce.number().int().min(1).max(365)),
    NEXT_PUBLIC_WEBAPP_COOKIE_DOMAIN: z.string(),
    NEXT_PUBLIC_WEBAPP_COOKIE_NAME: z.string(),
    NEXT_PUBLIC_WEBAPP_DISABLE_TRPC_LOGGER: z.enum(["true", "false"]).transform((value) => value === "true"),
    NEXT_PUBLIC_WEBAPP_FORMBRICKS_ENVIRONMENT_ID: z.string(),
    NEXT_PUBLIC_WEBAPP_FORMBRICKS_HOST: z.string(),
    NEXT_PUBLIC_WEBAPP_GOOGLE_TAG_MANAGER: googleTagManagerValidation,
    NEXT_PUBLIC_WEBAPP_IS_IN_MAINTENANCE_MODE: isMaintenanceModeValidation,
    NEXT_PUBLIC_WEBAPP_IS_REQUEST_BATCHING_DISABLED: z.enum(["true", "false"]).transform((value) => value === "true"),
    NEXT_PUBLIC_WEBAPP_MAXIMUM_FILE_UPLOAD_SIZE_IN_MB: z.string().pipe(z.coerce.number().int().min(1).max(999)),
    NEXT_PUBLIC_WEBAPP_MEILISEARCH_PUBLIC_URL: urlValidation,
    NEXT_PUBLIC_WEBAPP_MEILISEARCH_TENANT_TOKEN_EXPIRATION_TIME_MS: z
      .string()
      .pipe(z.coerce.number().int().min(10_000)),
    NEXT_PUBLIC_WEBAPP_POSTHOG: posthogConfigValidation,
    NEXT_PUBLIC_WEBAPP_RESEND_EMAIL_CONFIRMATION_TIMEOUT_IN_SECONDS: z
      .string()
      .pipe(z.coerce.number().int().min(1).max(3600)),
    NEXT_PUBLIC_WEBAPP_SIGN_UP_DEFAULT_EMAIL: z.string().email().optional(),
    NEXT_PUBLIC_WEBAPP_STREAK_DAILY_TIME_ACTIVITY_THRESHOLD_SECONDS: z
      .string()
      .pipe(z.coerce.number().int().min(1).max(86400)),
    NEXT_PUBLIC_WEBAPP_SUPABASE_ANON_KEY: z.string(),
    NEXT_PUBLIC_WEBAPP_SUPABASE_URL: urlValidation,
    NEXT_PUBLIC_WEBAPP_USER_ACTIVITY_PING_INTERVAL_SECONDS: z.string().pipe(z.coerce.number().int().min(1).max(3600)),
    NEXT_PUBLIC_WEBSITE_GOOGLE_TAG_MANAGER: googleTagManagerValidation,
    NEXT_PUBLIC_WEBSITE_IS_IN_MAINTENANCE_MODE: isMaintenanceModeValidation,
    NEXT_PUBLIC_WEBSITE_POSTHOG: posthogConfigValidation,
  },
  clientPrefix: "NEXT_PUBLIC_",
  emptyStringAsUndefined: true,
  runtimeEnvStrict: {
    CRON_SECRET: process.env.CRON_SECRET,
    NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT: process.env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT,
    NEXT_PUBLIC_SHARED_TRIAL_PERIOD_IN_DAYS: process.env.NEXT_PUBLIC_SHARED_TRIAL_PERIOD_IN_DAYS,
    NEXT_PUBLIC_SHARED_WEBAPP_URL: process.env.NEXT_PUBLIC_SHARED_WEBAPP_URL,
    NEXT_PUBLIC_SHARED_WEBSITE_URL: process.env.NEXT_PUBLIC_SHARED_WEBSITE_URL,
    NEXT_PUBLIC_WEBAPP_APP_NAME: process.env.NEXT_PUBLIC_WEBAPP_APP_NAME,
    NEXT_PUBLIC_WEBAPP_BACKEND_URL: process.env.NEXT_PUBLIC_WEBAPP_BACKEND_URL,
    NEXT_PUBLIC_WEBAPP_CLOUD_STORAGE_SIGNED_GET_URL_STALE_TIME_SECONDS:
      process.env.NEXT_PUBLIC_WEBAPP_CLOUD_STORAGE_SIGNED_GET_URL_STALE_TIME_SECONDS,
    NEXT_PUBLIC_WEBAPP_CONTENT_ITEMS_VIEWS_HISTORY_DAYS_LIMIT:
      process.env.NEXT_PUBLIC_WEBAPP_CONTENT_ITEMS_VIEWS_HISTORY_DAYS_LIMIT,
    NEXT_PUBLIC_WEBAPP_COOKIE_DOMAIN: process.env.NEXT_PUBLIC_WEBAPP_COOKIE_DOMAIN,
    NEXT_PUBLIC_WEBAPP_COOKIE_NAME: process.env.NEXT_PUBLIC_WEBAPP_COOKIE_NAME,
    NEXT_PUBLIC_WEBAPP_DISABLE_TRPC_LOGGER: process.env.NEXT_PUBLIC_WEBAPP_DISABLE_TRPC_LOGGER,
    NEXT_PUBLIC_WEBAPP_FORMBRICKS_ENVIRONMENT_ID: process.env.NEXT_PUBLIC_WEBAPP_FORMBRICKS_ENVIRONMENT_ID,
    NEXT_PUBLIC_WEBAPP_FORMBRICKS_HOST: process.env.NEXT_PUBLIC_WEBAPP_FORMBRICKS_HOST,
    NEXT_PUBLIC_WEBAPP_GOOGLE_TAG_MANAGER: process.env.NEXT_PUBLIC_WEBAPP_GOOGLE_TAG_MANAGER,
    NEXT_PUBLIC_WEBAPP_IS_IN_MAINTENANCE_MODE: process.env.NEXT_PUBLIC_WEBAPP_IS_IN_MAINTENANCE_MODE,
    NEXT_PUBLIC_WEBAPP_IS_REQUEST_BATCHING_DISABLED: process.env.NEXT_PUBLIC_WEBAPP_IS_REQUEST_BATCHING_DISABLED,
    NEXT_PUBLIC_WEBAPP_MAXIMUM_FILE_UPLOAD_SIZE_IN_MB: process.env.NEXT_PUBLIC_WEBAPP_MAXIMUM_FILE_UPLOAD_SIZE_IN_MB,
    NEXT_PUBLIC_WEBAPP_MEILISEARCH_PUBLIC_URL: process.env.NEXT_PUBLIC_WEBAPP_MEILISEARCH_PUBLIC_URL,
    NEXT_PUBLIC_WEBAPP_MEILISEARCH_TENANT_TOKEN_EXPIRATION_TIME_MS:
      process.env.NEXT_PUBLIC_WEBAPP_MEILISEARCH_TENANT_TOKEN_EXPIRATION_TIME_MS,
    NEXT_PUBLIC_WEBAPP_POSTHOG: process.env.NEXT_PUBLIC_WEBAPP_POSTHOG,
    NEXT_PUBLIC_WEBAPP_RESEND_EMAIL_CONFIRMATION_TIMEOUT_IN_SECONDS:
      process.env.NEXT_PUBLIC_WEBAPP_RESEND_EMAIL_CONFIRMATION_TIMEOUT_IN_SECONDS,
    NEXT_PUBLIC_WEBAPP_SIGN_UP_DEFAULT_EMAIL: process.env.NEXT_PUBLIC_WEBAPP_SIGN_UP_DEFAULT_EMAIL,
    NEXT_PUBLIC_WEBAPP_STREAK_DAILY_TIME_ACTIVITY_THRESHOLD_SECONDS:
      process.env.NEXT_PUBLIC_WEBAPP_STREAK_DAILY_TIME_ACTIVITY_THRESHOLD_SECONDS,
    NEXT_PUBLIC_WEBAPP_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_WEBAPP_SUPABASE_ANON_KEY,
    NEXT_PUBLIC_WEBAPP_SUPABASE_URL: process.env.NEXT_PUBLIC_WEBAPP_SUPABASE_URL,
    NEXT_PUBLIC_WEBAPP_USER_ACTIVITY_PING_INTERVAL_SECONDS:
      process.env.NEXT_PUBLIC_WEBAPP_USER_ACTIVITY_PING_INTERVAL_SECONDS,
    NEXT_PUBLIC_WEBSITE_GOOGLE_TAG_MANAGER: process.env.NEXT_PUBLIC_WEBSITE_GOOGLE_TAG_MANAGER,
    NEXT_PUBLIC_WEBSITE_IS_IN_MAINTENANCE_MODE: process.env.NEXT_PUBLIC_WEBSITE_IS_IN_MAINTENANCE_MODE,
    NEXT_PUBLIC_WEBSITE_POSTHOG: process.env.NEXT_PUBLIC_WEBSITE_POSTHOG,
    SHARED_CAISY_API_KEY: process.env.SHARED_CAISY_API_KEY,
    SHARED_LOOPS_API_KEY: process.env.SHARED_LOOPS_API_KEY,
    WEBAPP_CAISY_ARTICLE_BLUEPRINT_ID: process.env.WEBAPP_CAISY_ARTICLE_BLUEPRINT_ID,
    WEBAPP_CAISY_CASE_BLUEPRINT_ID: process.env.WEBAPP_CAISY_CASE_BLUEPRINT_ID,
    WEBAPP_CAISY_LEGAL_AREA_BLUEPRINT_ID: process.env.WEBAPP_CAISY_LEGAL_AREA_BLUEPRINT_ID,
    WEBAPP_CAISY_MAIN_CATEGORY_BLUEPRINT_ID: process.env.WEBAPP_CAISY_MAIN_CATEGORY_BLUEPRINT_ID,
    WEBAPP_CAISY_PREVIEW_SECRET: process.env.WEBAPP_CAISY_PREVIEW_SECRET,
    WEBAPP_CAISY_PREVIEW_USER_ID: process.env.WEBAPP_CAISY_PREVIEW_USER_ID,
    WEBAPP_CAISY_PROJECT_ID: process.env.WEBAPP_CAISY_PROJECT_ID,
    WEBAPP_CAISY_SUB_CATEGORY_BLUEPRINT_ID: process.env.WEBAPP_CAISY_SUB_CATEGORY_BLUEPRINT_ID,
    WEBAPP_CAISY_TAG_BLUEPRINT_ID: process.env.WEBAPP_CAISY_TAG_BLUEPRINT_ID,
    WEBAPP_CAISY_TOPIC_BLUEPRINT_ID: process.env.WEBAPP_CAISY_TOPIC_BLUEPRINT_ID,
    WEBAPP_CAISY_WEBHOOKS_SECRET_KEY: process.env.WEBAPP_CAISY_WEBHOOKS_SECRET_KEY,
    WEBAPP_CLEAR_REDIS_CACHE_SECRET: process.env.WEBAPP_CLEAR_REDIS_CACHE_SECRET,
    WEBAPP_CLICKUP_API_ENDPOINT: process.env.WEBAPP_CLICKUP_API_ENDPOINT,
    WEBAPP_CLICKUP_API_TOKEN: process.env.WEBAPP_CLICKUP_API_TOKEN,
    WEBAPP_CLICKUP_CONTENT_TASKS_LIST_ID: process.env.WEBAPP_CLICKUP_CONTENT_TASKS_LIST_ID,
    WEBAPP_CLICKUP_CRM_LIST_ID: process.env.WEBAPP_CLICKUP_CRM_LIST_ID,
    WEBAPP_CLICKUP_FEEDBACK_LIST_ID: process.env.WEBAPP_CLICKUP_FEEDBACK_LIST_ID,
    WEBAPP_CLICKUP_REFERRAL_PAYOUT_LIST_ID: process.env.WEBAPP_CLICKUP_REFERRAL_PAYOUT_LIST_ID,
    WEBAPP_COOKIE_SECRET: process.env.WEBAPP_COOKIE_SECRET,
    WEBAPP_DATABASE_URL: process.env.WEBAPP_DATABASE_URL,
    WEBAPP_DATABASE_URL_SESSION_POOLER: process.env.WEBAPP_DATABASE_URL_SESSION_POOLER,
    WEBAPP_FASTIFY_RATE_LIMIT_PER_MINUTE: process.env.WEBAPP_FASTIFY_RATE_LIMIT_PER_MINUTE,
    WEBAPP_GOOGLE_CLOUD_STORAGE_BUCKET_NAME: process.env.WEBAPP_GOOGLE_CLOUD_STORAGE_BUCKET_NAME,
    WEBAPP_GOOGLE_CLOUD_STORAGE_PROJECT_ID: process.env.WEBAPP_GOOGLE_CLOUD_STORAGE_PROJECT_ID,
    WEBAPP_GOOGLE_CLOUD_STORAGE_PUBLIC_BUCKET_NAME: process.env.WEBAPP_GOOGLE_CLOUD_STORAGE_PUBLIC_BUCKET_NAME,
    WEBAPP_GOOGLE_GENERATIVE_AI_API_KEY: process.env.WEBAPP_GOOGLE_GENERATIVE_AI_API_KEY,
    WEBAPP_GOOGLE_SERVICE_ACCOUNT_BASE64: process.env.WEBAPP_GOOGLE_SERVICE_ACCOUNT_BASE64,
    WEBAPP_LOG_LEVEL: process.env.WEBAPP_LOG_LEVEL,
    WEBAPP_MAX_HEAP_USED_BYTES: process.env.WEBAPP_MAX_HEAP_USED_BYTES,
    WEBAPP_MAX_RSS_BYTES: process.env.WEBAPP_MAX_RSS_BYTES,
    WEBAPP_MEILISEARCH_HOST_URL: process.env.WEBAPP_MEILISEARCH_HOST_URL,
    WEBAPP_MEILISEARCH_MASTER_API_KEY: process.env.WEBAPP_MEILISEARCH_MASTER_API_KEY,
    WEBAPP_POSTGRES_MAX_CONNECTIONS: process.env.WEBAPP_POSTGRES_MAX_CONNECTIONS,
    WEBAPP_RECREATE_SEARCH_INDEX_SECRET: process.env.WEBAPP_RECREATE_SEARCH_INDEX_SECRET,
    WEBAPP_REDIS_URL: process.env.WEBAPP_REDIS_URL,
    WEBAPP_SENTRY_AUTH_TOKEN: process.env.WEBAPP_SENTRY_AUTH_TOKEN,
    WEBAPP_SLACK_USER_NOTIFICATION_WEBHOOK_URL: process.env.WEBAPP_SLACK_USER_NOTIFICATION_WEBHOOK_URL,
    WEBAPP_STRIPE_LIFETIME_PRICE_ID: process.env.WEBAPP_STRIPE_LIFETIME_PRICE_ID,
    WEBAPP_STRIPE_PAYMENT_METHODS_CONFIGURATION_ID: process.env.WEBAPP_STRIPE_PAYMENT_METHODS_CONFIGURATION_ID,
    WEBAPP_STRIPE_PREMIUM_PLAN_PRICE_ID: process.env.WEBAPP_STRIPE_PREMIUM_PLAN_PRICE_ID,
    WEBAPP_STRIPE_PREMIUM_PLAN_YEARLY_PRICE_ID: process.env.WEBAPP_STRIPE_PREMIUM_PLAN_YEARLY_PRICE_ID,
    WEBAPP_STRIPE_SECRET_KEY: process.env.WEBAPP_STRIPE_SECRET_KEY,
    WEBAPP_STRIPE_SIGNING_SECRET: process.env.WEBAPP_STRIPE_SIGNING_SECRET,
    WEBAPP_SUPABASE_SERVICE_ROLE_KEY: process.env.WEBAPP_SUPABASE_SERVICE_ROLE_KEY,
    WEBAPP_SUPABASE_WEBHOOK_SECRET: process.env.WEBAPP_SUPABASE_WEBHOOK_SECRET,
    WEBAPP_SYNC_USERS_TO_CRM: process.env.WEBAPP_SYNC_USERS_TO_CRM,
    WEBAPP_THROTTLE_REQUESTS_IN_MS: process.env.WEBAPP_THROTTLE_REQUESTS_IN_MS,
    WEBSITE_BLOB_READ_WRITE_TOKEN: process.env.WEBSITE_BLOB_READ_WRITE_TOKEN,
    WEBSITE_CAISY_PROJECT_ID: process.env.WEBSITE_CAISY_PROJECT_ID,
    WEBSITE_FREE_ARTICLE_PER_MONTH_LIMIT: process.env.WEBSITE_FREE_ARTICLE_PER_MONTH_LIMIT,
  },
  server: {
    CRON_SECRET: z.string(),
    SHARED_CAISY_API_KEY: z.string(),
    SHARED_LOOPS_API_KEY: z.string(),
    WEBAPP_CAISY_ARTICLE_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_CASE_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_LEGAL_AREA_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_MAIN_CATEGORY_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_PREVIEW_SECRET: z.string(),
    WEBAPP_CAISY_PREVIEW_USER_ID: z.string(),
    WEBAPP_CAISY_PROJECT_ID: z.string(),
    WEBAPP_CAISY_SUB_CATEGORY_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_TAG_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_TOPIC_BLUEPRINT_ID: z.string(),
    WEBAPP_CAISY_WEBHOOKS_SECRET_KEY: z.string(),
    WEBAPP_CLEAR_REDIS_CACHE_SECRET: z.string(),
    WEBAPP_CLICKUP_API_ENDPOINT: urlValidation,
    WEBAPP_CLICKUP_API_TOKEN: z.string(),
    WEBAPP_CLICKUP_CONTENT_TASKS_LIST_ID: z.string(),
    WEBAPP_CLICKUP_CRM_LIST_ID: z.string(),
    WEBAPP_CLICKUP_FEEDBACK_LIST_ID: z.string(),
    WEBAPP_CLICKUP_REFERRAL_PAYOUT_LIST_ID: z.string(),
    WEBAPP_COOKIE_SECRET: z.string(),
    WEBAPP_DATABASE_URL: urlValidation,
    WEBAPP_DATABASE_URL_SESSION_POOLER: urlValidation,
    WEBAPP_FASTIFY_RATE_LIMIT_PER_MINUTE: z.string().pipe(z.coerce.number().int().min(1)),
    WEBAPP_GOOGLE_CLOUD_STORAGE_BUCKET_NAME: z.string(),
    WEBAPP_GOOGLE_CLOUD_STORAGE_PROJECT_ID: z.string(),
    WEBAPP_GOOGLE_CLOUD_STORAGE_PUBLIC_BUCKET_NAME: z.string(),
    WEBAPP_GOOGLE_GENERATIVE_AI_API_KEY: z.string(),
    WEBAPP_GOOGLE_SERVICE_ACCOUNT_BASE64: z.string(),
    WEBAPP_LOG_LEVEL: z.enum(["fatal", "error", "warn", "info", "debug", "trace"]),
    WEBAPP_MAX_HEAP_USED_BYTES: z.string().pipe(z.coerce.number().int().min(500_000_000)),
    WEBAPP_MAX_RSS_BYTES: z.string().pipe(z.coerce.number().int().min(500_000_000)),
    WEBAPP_MEILISEARCH_HOST_URL: urlValidation,
    WEBAPP_MEILISEARCH_MASTER_API_KEY: z.string(),
    WEBAPP_POSTGRES_MAX_CONNECTIONS: z.string().pipe(z.coerce.number().int().min(1).max(9999)),
    WEBAPP_RECREATE_SEARCH_INDEX_SECRET: z.string(),
    WEBAPP_REDIS_URL: z.string().url(),
    WEBAPP_SENTRY_AUTH_TOKEN: z.string(),
    WEBAPP_SLACK_USER_NOTIFICATION_WEBHOOK_URL: z.string().url(),
    WEBAPP_STRIPE_LIFETIME_PRICE_ID: z.string(),
    WEBAPP_STRIPE_PAYMENT_METHODS_CONFIGURATION_ID: z.string(),
    WEBAPP_STRIPE_PREMIUM_PLAN_PRICE_ID: z.string(),
    WEBAPP_STRIPE_PREMIUM_PLAN_YEARLY_PRICE_ID: z.string(),
    WEBAPP_STRIPE_SECRET_KEY: z.string(),
    WEBAPP_STRIPE_SIGNING_SECRET: z.string(),
    WEBAPP_SUPABASE_SERVICE_ROLE_KEY: z.string(),
    WEBAPP_SUPABASE_WEBHOOK_SECRET: z.string(),
    WEBAPP_SYNC_USERS_TO_CRM: z.enum(["true", "false"]).transform((value) => value === "true"),
    WEBAPP_THROTTLE_REQUESTS_IN_MS: z.string().pipe(z.coerce.number().int().min(1)).optional(),
    WEBSITE_BLOB_READ_WRITE_TOKEN: z.string(),
    WEBSITE_CAISY_PROJECT_ID: z.string(),
    WEBSITE_FREE_ARTICLE_PER_MONTH_LIMIT: z.string().pipe(z.coerce.number().int().min(1).max(9999)),
  },
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
});

export const isLocalhost = env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT === "localhost";
export const isDevelopment = env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT === "development";
export const isStaging = env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT === "staging";
export const isProduction = env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT === "production";

export const getIsDeploymentEnvironment = (
  ...environments: Array<(typeof env)["NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT"]>
) =>
{
  return environments.includes(env.NEXT_PUBLIC_SHARED_DEPLOYMENT_ENVIRONMENT);
};

export type Env = typeof env;
