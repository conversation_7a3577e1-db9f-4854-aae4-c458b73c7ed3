"use client";

import { BgLines2 } from "@/app/_components/common/bg-lines-2";
import {
  ContactForm,
  ContactFormHeader,
  ContactFormSubmitResult,
  useContactForm,
} from "@/app/_components/common/contact-form";
import { ContentWrapper } from "@/app/_components/layout/content-wrapper";

import { type FunctionComponent } from "react";

export const Contact: FunctionComponent = () =>
{
  const useContactFormData = useContactForm();

  return (
    <ContentWrapper className={"max-md:w-full"}>
      <div
        className={
          "relative rounded-3xl bg-cc-dictionary-2 px-28 py-24 max-xl:px-16 max-xl:py-20 max-md:px-content-wrapper max-md:py-16"
        }>
        <BgLines2
          bgColorRgbValues={[195, 223, 207]}
          forceMinWidth={false}
          linesColor={"white"}
          className={"absolute left-1/2 top-1/2 z-0 -translate-x-1/2 -translate-y-1/2 opacity-60"}
        />
        <div
          className={
            "relative z-10 flex items-start justify-between gap-20 max-lg:flex-col max-lg:items-stretch max-md:gap-12 "
          }>
          <div className={"max-w-[460px] flex-1 pt-2.5 max-lg:max-w-full"}>
            <div className={"w-full max-lg:hidden"}>
              <ContactFormHeader variant={"left"} badgeVariant={"white"} />
            </div>
            <div className={"hidden w-full max-lg:block"}>
              <ContactFormHeader variant={"centered"} badgeVariant={"white"} />
            </div>
          </div>
          <div
            className={
              "w-[550px] max-w-[34vw] max-lg:flex max-lg:w-full max-lg:max-w-full max-lg:flex-col max-lg:items-center"
            }>
            {useContactFormData.result != null ? (
              <ContactFormSubmitResult result={useContactFormData.result} />
            ) : (
              <ContactForm {...useContactFormData} />
            )}
          </div>
        </div>
      </div>
    </ContentWrapper>
  );
};
